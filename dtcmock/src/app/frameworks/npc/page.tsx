'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Hero from '../../../components/Hero';
import DAMAWheel from '../../../components/DAMAWheel';

export default function NPCFramework() {
  const [language, setLanguage] = useState<'en' | 'ar'>('en');
  const router = useRouter();

  useEffect(() => {
    const mockLanguage = localStorage.getItem('language') as 'en' | 'ar' || 'en';
    setLanguage(mockLanguage);
  }, []);

  const content = {
    en: {
      title: 'NPC Framework',
      subtitle: 'National Planning Council',
      description: 'Qatar\'s strategic framework for national planning, resource allocation, and performance management across government entities.',
    },
    ar: {
      title: 'إطار NPC',
      subtitle: 'مجلس التخطيط الوطني',
      description: 'إطار قطر الاستراتيجي للتخطيط الوطني وتخصيص الموارد وإدارة الأداء عبر الكيانات الحكومية.',
    }
  };

  const frameworkIcon = (
    <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
    </svg>
  );

  const handleDomainClick = (domain: string) => {
    router.push(`/frameworks/npc/${domain}`);
  };

  return (
    <div className={`${language === 'ar' ? 'text-right' : 'text-left'}`}>
      <Hero
        title={content[language].title}
        subtitle={content[language].subtitle}
        description={content[language].description}
        icon={frameworkIcon}
        breadcrumbs={[
          { label: language === 'en' ? 'Dashboard' : 'لوحة التحكم', href: '/dashboard' },
          { label: language === 'en' ? 'Frameworks' : 'الأطر', href: '/dashboard/frameworks' },
          { label: 'NPC' }
        ]}
      />

      <div className="bg-white">
        {/* Country Badge */}
        <div className="px-12 pt-8">
          <div className="flex items-center gap-3 mb-8">
            <div className="px-4 py-2 bg-red-600 text-white rounded-full flex items-center gap-2 font-bold">
              <span>🇶🇦</span>
              <span>{language === 'en' ? 'Qatar' : 'دولة قطر'}</span>
            </div>
          </div>
        </div>

        {/* DAMA Wheel */}
        <div className="px-12 pb-16">
          <div className="bg-white rounded-3xl shadow-lg p-8">
            <DAMAWheel 
              language={language} 
              framework="npc"
              onDomainClick={handleDomainClick}
            />
          </div>
        </div>
      </div>
    </div>
  );
}
