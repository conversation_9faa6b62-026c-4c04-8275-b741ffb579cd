'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Hero from '../../../components/Hero';
import EALayers from '../../../components/EALayers';

export default function GEAFramework() {
  const [language, setLanguage] = useState<'en' | 'ar'>('en');
  const router = useRouter();

  useEffect(() => {
    const mockLanguage = localStorage.getItem('language') as 'en' | 'ar' || 'en';
    setLanguage(mockLanguage);
  }, []);

  const content = {
    en: {
      title: 'GEA Framework',
      subtitle: 'Government Enterprise Architecture',
      description: 'Qatar\'s government enterprise architecture framework for digital transformation and integrated government services.',
    },
    ar: {
      title: 'إطار GEA',
      subtitle: 'هندسة المؤسسة الحكومية',
      description: 'إطار هندسة المؤسسة الحكومية لدولة قطر للتحول الرقمي والخدمات الحكومية المتكاملة.',
    }
  };

  const frameworkIcon = (
    <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
    </svg>
  );

  const handleLayerClick = (layer: string) => {
    router.push(`/frameworks/gea/${layer}`);
  };

  return (
    <div className={`${language === 'ar' ? 'text-right' : 'text-left'}`}>
      <Hero
        title={content[language].title}
        subtitle={content[language].subtitle}
        description={content[language].description}
        icon={frameworkIcon}
        breadcrumbs={[
          { label: language === 'en' ? 'Dashboard' : 'لوحة التحكم', href: '/dashboard' },
          { label: language === 'en' ? 'Frameworks' : 'الأطر', href: '/dashboard/frameworks' },
          { label: 'GEA' }
        ]}
      />

      <div className="bg-white">
        {/* Country Badge */}
        <div className="px-12 pt-8">
          <div className="flex items-center gap-3 mb-8">
            <div className="px-4 py-2 bg-red-600 text-white rounded-full flex items-center gap-2 font-bold">
              <span>🇶🇦</span>
              <span>{language === 'en' ? 'Qatar' : 'دولة قطر'}</span>
            </div>
          </div>
        </div>

        {/* EA Layers */}
        <div className="px-12 pb-16">
          <div className="bg-white rounded-3xl shadow-lg p-8">
            <EALayers 
              language={language} 
              onLayerClick={handleLayerClick}
            />
          </div>
        </div>
      </div>
    </div>
  );
}
