'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Hero from '../../../components/Hero';
import DAMAWheel from '../../../components/DAMAWheel';

export default function NDMOFramework() {
  const [language, setLanguage] = useState<'en' | 'ar'>('en');
  const router = useRouter();

  useEffect(() => {
    const mockLanguage = localStorage.getItem('language') as 'en' | 'ar' || 'en';
    setLanguage(mockLanguage);
  }, []);

  const content = {
    en: {
      title: 'NDMO Framework',
      subtitle: 'National Data Management Office',
      description: 'Saudi Arabia\'s comprehensive framework for national data management, governance, and digital transformation initiatives.',
    },
    ar: {
      title: 'إطار NDMO',
      subtitle: 'مكتب إدارة البيانات الوطني',
      description: 'إطار المملكة العربية السعودية الشامل لإدارة البيانات الوطنية والحوكمة ومبادرات التحول الرقمي.',
    }
  };

  const frameworkIcon = (
    <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4m0 5c0 2.21-3.582 4-8 4s-8-1.79-8-4" />
    </svg>
  );

  const handleDomainClick = (domain: string) => {
    router.push(`/frameworks/ndmo/${domain}`);
  };

  return (
    <div className={`${language === 'ar' ? 'text-right' : 'text-left'}`}>
      <Hero
        title={content[language].title}
        subtitle={content[language].subtitle}
        description={content[language].description}
        icon={frameworkIcon}
        breadcrumbs={[
          { label: language === 'en' ? 'Dashboard' : 'لوحة التحكم', href: '/dashboard' },
          { label: language === 'en' ? 'Frameworks' : 'الأطر', href: '/dashboard/frameworks' },
          { label: 'NDMO' }
        ]}
      />

      <div className="bg-white">
        {/* Country Badge */}
        <div className="px-12 pt-8">
          <div className="flex items-center gap-3 mb-8">
            <div className="px-4 py-2 bg-green-600 text-white rounded-full flex items-center gap-2 font-bold">
              <span>🇸🇦</span>
              <span>{language === 'en' ? 'Saudi Arabia' : 'المملكة العربية السعودية'}</span>
            </div>
          </div>
        </div>

        {/* DAMA Wheel */}
        <div className="px-12 pb-16">
          <div className="bg-white rounded-3xl shadow-lg p-8">
            <DAMAWheel 
              language={language} 
              framework="ndmo"
              onDomainClick={handleDomainClick}
            />
          </div>
        </div>
      </div>
    </div>
  );
}
