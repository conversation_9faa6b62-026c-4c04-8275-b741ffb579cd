'use client';

import { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import Hero from '../../../../components/Hero';

export default function NDMODomainPage() {
  const [language, setLanguage] = useState<'en' | 'ar'>('en');
  const [activeSection, setActiveSection] = useState('overview');
  const params = useParams();
  const router = useRouter();
  const domain = params.domain as string;

  useEffect(() => {
    const mockLanguage = localStorage.getItem('language') as 'en' | 'ar' || 'en';
    setLanguage(mockLanguage);
  }, []);

  const domainSpecs = {
    'data-governance': {
      en: {
        title: 'Data Governance',
        description: 'Comprehensive framework for establishing data governance policies, procedures, and organizational structures within NDMO.',
        sections: [
          {
            id: 'policies',
            title: 'Governance Policies',
            content: 'Define data ownership, stewardship roles, and decision-making authority across Saudi government entities. Establish clear accountability frameworks and governance structures.'
          },
          {
            id: 'standards',
            title: 'Data Standards',
            content: 'Establish consistent data definitions, formats, and quality requirements aligned with Saudi Vision 2030 objectives and national digital transformation goals.'
          },
          {
            id: 'compliance',
            title: 'Compliance Framework',
            content: 'Ensure adherence to Saudi regulatory requirements, international standards, and data protection laws including PDPL (Personal Data Protection Law).'
          }
        ]
      },
      ar: {
        title: 'حوكمة البيانات',
        description: 'إطار شامل لوضع سياسات وإجراءات وهياكل تنظيمية لحوكمة البيانات داخل مكتب إدارة البيانات الوطني.',
        sections: [
          {
            id: 'policies',
            title: 'سياسات الحوكمة',
            content: 'تحديد ملكية البيانات وأدوار الإشراف وسلطة اتخاذ القرار عبر الكيانات الحكومية السعودية. وضع أطر مساءلة واضحة وهياكل حوكمة.'
          },
          {
            id: 'standards',
            title: 'معايير البيانات',
            content: 'وضع تعريفات وتنسيقات ومتطلبات جودة متسقة للبيانات متماشية مع أهداف رؤية السعودية 2030 وأهداف التحول الرقمي الوطني.'
          },
          {
            id: 'compliance',
            title: 'إطار الامتثال',
            content: 'ضمان الالتزام بالمتطلبات التنظيمية السعودية والمعايير الدولية وقوانين حماية البيانات بما في ذلك قانون حماية البيانات الشخصية.'
          }
        ]
      }
    },
    'data-architecture': {
      en: {
        title: 'Data Architecture',
        description: 'Technical blueprint for data systems, integration patterns, and infrastructure design within the NDMO framework.',
        sections: [
          {
            id: 'design',
            title: 'Architecture Design',
            content: 'Define data flow patterns, system interfaces, and integration architectures for Saudi government data systems and national data platforms.'
          },
          {
            id: 'infrastructure',
            title: 'Infrastructure Requirements',
            content: 'Specify hardware, software, and network requirements for national data systems, including cloud infrastructure and security specifications.'
          },
          {
            id: 'integration',
            title: 'Integration Patterns',
            content: 'Establish standardized integration patterns for connecting government entities, private sector partners, and international data exchanges.'
          }
        ]
      },
      ar: {
        title: 'هندسة البيانات',
        description: 'مخطط تقني لأنظمة البيانات وأنماط التكامل وتصميم البنية التحتية ضمن إطار مكتب إدارة البيانات الوطني.',
        sections: [
          {
            id: 'design',
            title: 'تصميم الهندسة',
            content: 'تحديد أنماط تدفق البيانات وواجهات النظام وهندسة التكامل لأنظمة البيانات الحكومية السعودية والمنصات الوطنية للبيانات.'
          },
          {
            id: 'infrastructure',
            title: 'متطلبات البنية التحتية',
            content: 'تحديد متطلبات الأجهزة والبرمجيات والشبكة لأنظمة البيانات الوطنية، بما في ذلك البنية التحتية السحابية ومواصفات الأمان.'
          },
          {
            id: 'integration',
            title: 'أنماط التكامل',
            content: 'وضع أنماط تكامل موحدة لربط الكيانات الحكومية وشركاء القطاع الخاص وتبادل البيانات الدولية.'
          }
        ]
      }
    }
  };

  const currentSpec = domainSpecs[domain as keyof typeof domainSpecs]?.[language];

  if (!currentSpec) {
    return (
      <div className="p-8 text-center">
        <p className={`text-gray-500 ${language === 'ar' ? 'font-arabic' : ''}`}>
          {language === 'en' ? 'Domain specification not found' : 'مواصفات المجال غير موجودة'}
        </p>
      </div>
    );
  }

  const domainIcon = (
    <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
    </svg>
  );

  return (
    <div className={`${language === 'ar' ? 'text-right' : 'text-left'}`}>
      <Hero
        title={currentSpec.title}
        subtitle="NDMO Framework"
        description={currentSpec.description}
        icon={domainIcon}
        breadcrumbs={[
          { label: language === 'en' ? 'Dashboard' : 'لوحة التحكم', href: '/dashboard' },
          { label: language === 'en' ? 'Frameworks' : 'الأطر', href: '/dashboard/frameworks' },
          { label: 'NDMO', href: '/frameworks/ndmo' },
          { label: currentSpec.title }
        ]}
      />

      <div className="bg-white">
        <div className="px-12 py-16">
          {/* Country Badge */}
          <div className="flex items-center gap-3 mb-8">
            <div className="px-4 py-2 bg-green-600 text-white rounded-full flex items-center gap-2 font-bold">
              <span>🇸🇦</span>
              <span>{language === 'en' ? 'Saudi Arabia' : 'المملكة العربية السعودية'}</span>
            </div>
          </div>

          {/* Content */}
          <div className={`flex gap-8 ${language === 'ar' ? 'flex-row-reverse' : 'flex-row'}`}>
            {/* Navigation */}
            <div className="w-64">
              <div className="bg-white rounded-2xl shadow-lg p-6 sticky top-8">
                <h3 className={`text-lg font-bold mb-4 ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--emerald-green)' }}>
                  {language === 'en' ? 'Sections' : 'الأقسام'}
                </h3>
                <nav className="space-y-2">
                  <button
                    onClick={() => setActiveSection('overview')}
                    className={`w-full text-left p-3 rounded-lg transition-colors ${
                      activeSection === 'overview' ? 'bg-emerald-50 text-emerald-700' : 'hover:bg-gray-50'
                    } ${language === 'ar' ? 'text-right font-arabic' : ''}`}
                  >
                    {language === 'en' ? 'Overview' : 'نظرة عامة'}
                  </button>
                  {currentSpec.sections.map((section) => (
                    <button
                      key={section.id}
                      onClick={() => setActiveSection(section.id)}
                      className={`w-full text-left p-3 rounded-lg transition-colors ${
                        activeSection === section.id ? 'bg-emerald-50 text-emerald-700' : 'hover:bg-gray-50'
                      } ${language === 'ar' ? 'text-right font-arabic' : ''}`}
                    >
                      {section.title}
                    </button>
                  ))}
                </nav>
              </div>
            </div>

            {/* Main Content */}
            <div className="flex-1">
              <div className="bg-white rounded-2xl shadow-lg p-8">
                {activeSection === 'overview' ? (
                  <div>
                    <h3 className={`text-2xl font-bold mb-4 ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--charcoal-grey)' }}>
                      {language === 'en' ? 'Overview' : 'نظرة عامة'}
                    </h3>
                    <p className={`text-gray-700 leading-relaxed text-lg ${language === 'ar' ? 'font-arabic' : ''}`}>
                      {currentSpec.description}
                    </p>
                  </div>
                ) : (
                  <div>
                    {currentSpec.sections.map((section) => (
                      activeSection === section.id && (
                        <div key={section.id}>
                          <h3 className={`text-2xl font-bold mb-6 ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--charcoal-grey)' }}>
                            {section.title}
                          </h3>
                          <div className={`prose max-w-none ${language === 'ar' ? 'font-arabic' : ''}`}>
                            <p className="text-gray-700 leading-relaxed text-lg">
                              {section.content}
                            </p>
                          </div>
                        </div>
                      )
                    ))}
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
