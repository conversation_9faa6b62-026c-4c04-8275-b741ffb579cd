'use client';

import { useState, useEffect } from 'react';

interface DAMAWheelProps {
  language: 'en' | 'ar';
  framework: 'ndmo' | 'npc';
  onDomainClick: (domain: string) => void;
}

export default function DAMAWheel({ language, framework, onDomainClick }: DAMAWheelProps) {
  const [selectedDomain, setSelectedDomain] = useState<string | null>(null);

  const ndmoDomains = [
    {
      id: 'data-governance',
      name: { en: 'Data Governance', ar: 'حوكمة البيانات' },
      angle: 0,
      color: '#026c4a'
    },
    {
      id: 'data-architecture',
      name: { en: 'Data Architecture', ar: 'هندسة البيانات' },
      angle: 45,
      color: '#0c402e'
    },
    {
      id: 'data-modeling',
      name: { en: 'Data Modeling', ar: 'نمذجة البيانات' },
      angle: 90,
      color: '#026c4a'
    },
    {
      id: 'data-storage',
      name: { en: 'Data Storage', ar: 'تخزين البيانات' },
      angle: 135,
      color: '#0c402e'
    },
    {
      id: 'data-security',
      name: { en: 'Data Security', ar: 'أمان البيانات' },
      angle: 180,
      color: '#026c4a'
    },
    {
      id: 'data-integration',
      name: { en: 'Data Integration', ar: 'تكامل البيانات' },
      angle: 225,
      color: '#0c402e'
    },
    {
      id: 'data-quality',
      name: { en: 'Data Quality', ar: 'جودة البيانات' },
      angle: 270,
      color: '#026c4a'
    },
    {
      id: 'metadata',
      name: { en: 'Metadata', ar: 'البيانات الوصفية' },
      angle: 315,
      color: '#0c402e'
    }
  ];

  const npcDomains = [
    {
      id: 'strategic-planning',
      name: { en: 'Strategic Planning', ar: 'التخطيط الاستراتيجي' },
      angle: 0,
      color: '#026c4a'
    },
    {
      id: 'performance-management',
      name: { en: 'Performance Management', ar: 'إدارة الأداء' },
      angle: 45,
      color: '#0c402e'
    },
    {
      id: 'resource-allocation',
      name: { en: 'Resource Allocation', ar: 'تخصيص الموارد' },
      angle: 90,
      color: '#026c4a'
    },
    {
      id: 'policy-development',
      name: { en: 'Policy Development', ar: 'تطوير السياسات' },
      angle: 135,
      color: '#0c402e'
    },
    {
      id: 'monitoring-evaluation',
      name: { en: 'Monitoring & Evaluation', ar: 'المراقبة والتقييم' },
      angle: 180,
      color: '#026c4a'
    },
    {
      id: 'stakeholder-engagement',
      name: { en: 'Stakeholder Engagement', ar: 'إشراك أصحاب المصلحة' },
      angle: 225,
      color: '#0c402e'
    },
    {
      id: 'risk-management',
      name: { en: 'Risk Management', ar: 'إدارة المخاطر' },
      angle: 270,
      color: '#026c4a'
    },
    {
      id: 'innovation-development',
      name: { en: 'Innovation Development', ar: 'تطوير الابتكار' },
      angle: 315,
      color: '#0c402e'
    }
  ];

  const domains = framework === 'ndmo' ? ndmoDomains : npcDomains;

  const handleDomainClick = (domain: any) => {
    setSelectedDomain(domain.id);
    onDomainClick(domain.id);
  };

  return (
    <div className="flex flex-col items-center justify-center p-8">
      <h3 className={`text-2xl font-bold mb-8 ${language === 'ar' ? 'font-arabic' : ''}`} style={{ color: 'var(--charcoal-grey)' }}>
        {language === 'en' ? 'DAMA Data Management Domains' : 'مجالات إدارة البيانات DAMA'}
      </h3>

      <div className="relative w-[500px] h-[500px] mx-auto">
        {/* Center Circle */}
        <div
          className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-32 h-32 rounded-full flex items-center justify-center text-white font-bold text-xl shadow-2xl"
          style={{ backgroundColor: 'var(--emerald-green)' }}
        >
          <span className={language === 'ar' ? 'font-arabic' : ''}>
            {language === 'en' ? 'DAMA' : 'داما'}
          </span>
        </div>

        {/* Domain Segments */}
        {domains.map((domain, index) => {
          const radius = 180;
          const centerX = 250;
          const centerY = 250;
          const angleRad = (domain.angle * Math.PI) / 180;
          const x = centerX + radius * Math.cos(angleRad);
          const y = centerY + radius * Math.sin(angleRad);

          return (
            <div key={domain.id} className="absolute inset-0 pointer-events-none">
              {/* Connection Line */}
              <div
                className="absolute top-1/2 left-1/2 origin-left h-0.5 opacity-30"
                style={{
                  width: `${radius - 64}px`,
                  backgroundColor: domain.color,
                  transform: `translate(-50%, -50%) rotate(${domain.angle}deg)`,
                  transformOrigin: 'left center'
                }}
              ></div>

              {/* Domain Circle */}
              <div
                className={`absolute transform -translate-x-1/2 -translate-y-1/2 cursor-pointer transition-all duration-500 hover:scale-110 pointer-events-auto z-20 ${
                  selectedDomain === domain.id ? 'scale-125' : ''
                }`}
                style={{
                  left: x,
                  top: y
                }}
                onClick={() => handleDomainClick(domain)}
              >
                <div
                  className={`w-24 h-24 rounded-full flex items-center justify-center text-white font-semibold text-xs shadow-lg hover:shadow-2xl transition-all duration-300 ${
                    selectedDomain === domain.id ? 'ring-4 ring-white' : ''
                  }`}
                  style={{ backgroundColor: domain.color }}
                >
                  <span className={`text-center leading-tight px-2 ${language === 'ar' ? 'font-arabic' : ''}`}>
                    {domain.name[language]}
                  </span>
                </div>
              </div>
            </div>
          );
        })}

        {/* Outer Ring */}
        <div className="absolute inset-8 border-2 border-gray-200 rounded-full opacity-50"></div>
      </div>
    </div>
  );
}
