{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/DTC%20Accelerator/dtcmock/src/components/Hero.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\n\ninterface HeroProps {\n  title: string;\n  subtitle?: string;\n  description?: string;\n  icon?: React.ReactNode;\n  breadcrumbs?: Array<{ label: string; href?: string }>;\n}\n\nexport default function Hero({ title, subtitle, description, icon, breadcrumbs }: HeroProps) {\n  const [language, setLanguage] = useState<'en' | 'ar'>('en');\n\n  useEffect(() => {\n    const mockLanguage = localStorage.getItem('language') as 'en' | 'ar' || 'en';\n    setLanguage(mockLanguage);\n  }, []);\n\n  return (\n    <div\n      className={`relative overflow-hidden ${language === 'ar' ? 'text-right' : 'text-left'}`}\n      style={{\n        background: 'linear-gradient(135deg, var(--emerald-green) 0%, var(--deep-emerald) 100%)',\n      }}\n    >\n\n      {/* Geometric Shapes */}\n      <div className=\"absolute inset-0 overflow-hidden\">\n        <div className={`absolute top-8 w-32 h-32 rounded-full bg-white/5 ${language === 'ar' ? 'right-8' : 'left-8'}`}></div>\n        <div className={`absolute bottom-8 w-24 h-24 rounded-lg bg-white/10 rotate-45 ${language === 'ar' ? 'left-16' : 'right-16'}`}></div>\n        <div className={`absolute top-1/2 w-16 h-16 rounded-full bg-white/5 ${language === 'ar' ? 'left-1/4' : 'right-1/4'}`}></div>\n      </div>\n\n      {/* Content */}\n      <div className=\"relative px-12 py-16\">\n        {/* Breadcrumbs */}\n        {breadcrumbs && breadcrumbs.length > 0 && (\n          <nav className=\"mb-6\">\n            <ol className={`flex items-center space-x-2 text-sm text-white/80 ${language === 'ar' ? 'flex-row-reverse space-x-reverse' : ''}`}>\n              {breadcrumbs.map((crumb, index) => (\n                <li key={index} className=\"flex items-center\">\n                  {index > 0 && (\n                    <svg \n                      className={`w-4 h-4 ${language === 'ar' ? 'ml-2 rotate-180' : 'mr-2'}`} \n                      fill=\"none\" \n                      stroke=\"currentColor\" \n                      viewBox=\"0 0 24 24\"\n                    >\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 5l7 7-7 7\" />\n                    </svg>\n                  )}\n                  {crumb.href ? (\n                    <a \n                      href={crumb.href} \n                      className=\"hover:text-white transition-colors font-medium\"\n                    >\n                      {crumb.label}\n                    </a>\n                  ) : (\n                    <span className=\"text-white font-medium\">{crumb.label}</span>\n                  )}\n                </li>\n              ))}\n            </ol>\n          </nav>\n        )}\n\n        {/* Main Content */}\n        <div className={`flex items-center ${language === 'ar' ? 'flex-row-reverse' : 'flex-row'}`}>\n          {/* Icon */}\n          {icon && (\n            <div className={`flex-shrink-0 ${language === 'ar' ? 'ml-6' : 'mr-6'}`}>\n              <div className=\"w-16 h-16 rounded-2xl bg-white/20 backdrop-blur-sm flex items-center justify-center border border-white/30 shadow-lg\">\n                <div className=\"text-white\">\n                  {icon}\n                </div>\n              </div>\n            </div>\n          )}\n\n          {/* Text Content */}\n          <div className=\"flex-1\">\n            {/* Subtitle */}\n            {subtitle && (\n              <p className={`text-white/90 text-sm font-medium mb-2 uppercase tracking-wider ${language === 'ar' ? 'font-arabic' : ''}`}>\n                {subtitle}\n              </p>\n            )}\n\n            {/* Title */}\n            <h1 className={`text-4xl md:text-5xl font-bold text-white mb-4 leading-tight ${language === 'ar' ? 'font-arabic' : ''}`}>\n              {title}\n            </h1>\n\n            {/* Description */}\n            {description && (\n              <p className={`text-white/90 text-lg leading-relaxed max-w-3xl ${language === 'ar' ? 'font-arabic' : ''}`}>\n                {description}\n              </p>\n            )}\n          </div>\n        </div>\n\n        {/* Bottom Accent Line */}\n        <div className=\"absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-white/20 via-white/40 to-white/20\"></div>\n      </div>\n\n      {/* Animated Gradient Overlay */}\n      <div className=\"absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent animate-pulse-subtle\"></div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAYe,SAAS,KAAK,EAAE,KAAK,EAAE,QAAQ,EAAE,WAAW,EAAE,IAAI,EAAE,WAAW,EAAa;IACzF,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IAEtD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,eAAe,aAAa,OAAO,CAAC,eAA8B;QACxE,YAAY;IACd,GAAG,EAAE;IAEL,qBACE,8OAAC;QACC,WAAW,CAAC,yBAAyB,EAAE,aAAa,OAAO,eAAe,aAAa;QACvF,OAAO;YACL,YAAY;QACd;;0BAIA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAW,CAAC,iDAAiD,EAAE,aAAa,OAAO,YAAY,UAAU;;;;;;kCAC9G,8OAAC;wBAAI,WAAW,CAAC,6DAA6D,EAAE,aAAa,OAAO,YAAY,YAAY;;;;;;kCAC5H,8OAAC;wBAAI,WAAW,CAAC,mDAAmD,EAAE,aAAa,OAAO,aAAa,aAAa;;;;;;;;;;;;0BAItH,8OAAC;gBAAI,WAAU;;oBAEZ,eAAe,YAAY,MAAM,GAAG,mBACnC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAG,WAAW,CAAC,kDAAkD,EAAE,aAAa,OAAO,qCAAqC,IAAI;sCAC9H,YAAY,GAAG,CAAC,CAAC,OAAO,sBACvB,8OAAC;oCAAe,WAAU;;wCACvB,QAAQ,mBACP,8OAAC;4CACC,WAAW,CAAC,QAAQ,EAAE,aAAa,OAAO,oBAAoB,QAAQ;4CACtE,MAAK;4CACL,QAAO;4CACP,SAAQ;sDAER,cAAA,8OAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAa;gDAAG,GAAE;;;;;;;;;;;wCAGxE,MAAM,IAAI,iBACT,8OAAC;4CACC,MAAM,MAAM,IAAI;4CAChB,WAAU;sDAET,MAAM,KAAK;;;;;iEAGd,8OAAC;4CAAK,WAAU;sDAA0B,MAAM,KAAK;;;;;;;mCAnBhD;;;;;;;;;;;;;;;kCA4BjB,8OAAC;wBAAI,WAAW,CAAC,kBAAkB,EAAE,aAAa,OAAO,qBAAqB,YAAY;;4BAEvF,sBACC,8OAAC;gCAAI,WAAW,CAAC,cAAc,EAAE,aAAa,OAAO,SAAS,QAAQ;0CACpE,cAAA,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;kDACZ;;;;;;;;;;;;;;;;0CAOT,8OAAC;gCAAI,WAAU;;oCAEZ,0BACC,8OAAC;wCAAE,WAAW,CAAC,gEAAgE,EAAE,aAAa,OAAO,gBAAgB,IAAI;kDACtH;;;;;;kDAKL,8OAAC;wCAAG,WAAW,CAAC,6DAA6D,EAAE,aAAa,OAAO,gBAAgB,IAAI;kDACpH;;;;;;oCAIF,6BACC,8OAAC;wCAAE,WAAW,CAAC,gDAAgD,EAAE,aAAa,OAAO,gBAAgB,IAAI;kDACtG;;;;;;;;;;;;;;;;;;kCAOT,8OAAC;wBAAI,WAAU;;;;;;;;;;;;0BAIjB,8OAAC;gBAAI,WAAU;;;;;;;;;;;;AAGrB", "debugId": null}}, {"offset": {"line": 212, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/DTC%20Accelerator/dtcmock/src/app/dashboard/frameworks/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { useRouter } from 'next/navigation';\nimport Hero from '../../../components/Hero';\n\ninterface Framework {\n  id: string;\n  name: string;\n  country: 'saudi' | 'qatar';\n  type: 'data-management' | 'enterprise-architecture';\n}\n\nconst frameworks: Framework[] = [\n  { id: 'ndmo', name: 'NDMO', country: 'saudi', type: 'data-management' },\n  { id: 'npc', name: 'NPC', country: 'qatar', type: 'data-management' },\n  { id: 'noura', name: 'NOURA', country: 'saudi', type: 'enterprise-architecture' },\n  { id: 'gea', name: 'GEA', country: 'qatar', type: 'enterprise-architecture' }\n];\n\nexport default function FrameworkManagement() {\n  const [language, setLanguage] = useState<'en' | 'ar'>('en');\n  const [selectedCategory, setSelectedCategory] = useState<'main' | 'data-management' | 'enterprise-architecture'>('main');\n  const router = useRouter();\n\n  useEffect(() => {\n    const mockLanguage = localStorage.getItem('language') as 'en' | 'ar' || 'en';\n    setLanguage(mockLanguage);\n  }, []);\n\n  const content = {\n    en: {\n      title: 'Framework Management',\n      subtitle: 'Digital Transformation',\n      description: 'Manage digital transformation frameworks, methodologies, and best practices to drive organizational change.',\n      placeholder: 'Framework management functionality will be implemented here.'\n    },\n    ar: {\n      title: 'إدارة الإطار',\n      subtitle: 'التحول الرقمي',\n      description: 'إدارة أطر ومنهجيات وأفضل الممارسات للتحول الرقمي لدفع التغيير التنظيمي.',\n      placeholder: 'سيتم تنفيذ وظائف إدارة الإطار هنا.'\n    }\n  };\n\n  const frameworkIcon = (\n    <svg className=\"w-8 h-8\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10\" />\n    </svg>\n  );\n\n  return (\n    <div className={`${language === 'ar' ? 'text-right' : 'text-left'}`}>\n      <Hero\n        title={content[language].title}\n        subtitle={content[language].subtitle}\n        description={content[language].description}\n        icon={frameworkIcon}\n        breadcrumbs={[\n          { label: language === 'en' ? 'Dashboard' : 'لوحة التحكم', href: '/dashboard' },\n          { label: content[language].title }\n        ]}\n      />\n\n      <div className=\"bg-white\">\n        {selectedCategory === 'main' && (\n          <div className=\"px-12 py-20\">\n            {/* Two Larger Enhanced Cards */}\n            <div className={`flex gap-16 ${language === 'ar' ? 'flex-row-reverse' : 'flex-row'}`}>\n\n              {/* Data Management Card - Enhanced */}\n              <div className=\"flex-1 group cursor-pointer\" onClick={() => setSelectedCategory('data-management')}>\n                <div className=\"relative overflow-hidden rounded-3xl h-96 transition-all duration-700 ease-out transform hover:scale-105 hover:shadow-2xl\">\n                  {/* Enhanced Background with Pattern */}\n                  <div\n                    className=\"absolute inset-0 transition-all duration-700 ease-out\"\n                    style={{\n                      background: 'linear-gradient(135deg, var(--emerald-green) 0%, var(--deep-emerald) 100%)'\n                    }}\n                  ></div>\n\n                  {/* Animated Background Pattern */}\n                  <div className=\"absolute inset-0 opacity-10\">\n                    <div className=\"absolute top-8 right-8 w-32 h-32 rounded-full bg-white animate-pulse-subtle\"></div>\n                    <div className=\"absolute bottom-8 left-8 w-24 h-24 rounded-lg bg-white rotate-45 animate-pulse-subtle\" style={{ animationDelay: '1s' }}></div>\n                    <div className=\"absolute top-1/2 left-1/3 w-16 h-16 rounded-full bg-white animate-pulse-subtle\" style={{ animationDelay: '2s' }}></div>\n                  </div>\n\n                  {/* Blade Effect - Enhanced */}\n                  <div className=\"absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-700\">\n                    <div\n                      className=\"absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent transform -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-1200 ease-out\"\n                      style={{ width: '200%' }}\n                    ></div>\n                  </div>\n\n                  {/* Enhanced Content */}\n                  <div className=\"relative h-full flex flex-col justify-center items-center text-center p-12\">\n                    <div className=\"w-24 h-24 rounded-3xl bg-white/20 backdrop-blur-sm flex items-center justify-center mb-8 group-hover:scale-110 transition-transform duration-500 shadow-2xl\">\n                      <svg className=\"w-12 h-12 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4m0 5c0 2.21-3.582 4-8 4s-8-1.79-8-4\" />\n                      </svg>\n                    </div>\n                    <h3 className={`text-4xl font-bold text-white mb-4 ${language === 'ar' ? 'font-arabic' : ''}`}>\n                      {language === 'en' ? 'Data Management' : 'إدارة البيانات'}\n                    </h3>\n                    <p className={`text-white/80 text-lg ${language === 'ar' ? 'font-arabic' : ''}`}>\n                      {language === 'en' ? 'Frameworks & Standards' : 'الأطر والمعايير'}\n                    </p>\n                  </div>\n                </div>\n              </div>\n\n              {/* Enterprise Architecture Card - Enhanced */}\n              <div className=\"flex-1 group cursor-pointer\" onClick={() => setSelectedCategory('enterprise-architecture')}>\n                <div className=\"relative overflow-hidden rounded-3xl h-96 transition-all duration-700 ease-out transform hover:scale-105 hover:shadow-2xl\">\n                  {/* Enhanced Background with Pattern */}\n                  <div\n                    className=\"absolute inset-0 transition-all duration-700 ease-out\"\n                    style={{\n                      background: 'linear-gradient(135deg, var(--deep-emerald) 0%, var(--charcoal-grey) 100%)'\n                    }}\n                  ></div>\n\n                  {/* Animated Background Pattern */}\n                  <div className=\"absolute inset-0 opacity-10\">\n                    <div className=\"absolute top-8 left-8 w-32 h-32 rounded-full bg-white animate-pulse-subtle\"></div>\n                    <div className=\"absolute bottom-8 right-8 w-24 h-24 rounded-lg bg-white rotate-45 animate-pulse-subtle\" style={{ animationDelay: '1s' }}></div>\n                    <div className=\"absolute top-1/2 right-1/3 w-16 h-16 rounded-full bg-white animate-pulse-subtle\" style={{ animationDelay: '2s' }}></div>\n                  </div>\n\n                  {/* Blade Effect - Enhanced */}\n                  <div className=\"absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-700\">\n                    <div\n                      className=\"absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent transform -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-1200 ease-out\"\n                      style={{ width: '200%' }}\n                    ></div>\n                  </div>\n\n                  {/* Enhanced Content */}\n                  <div className=\"relative h-full flex flex-col justify-center items-center text-center p-12\">\n                    <div className=\"w-24 h-24 rounded-3xl bg-white/20 backdrop-blur-sm flex items-center justify-center mb-8 group-hover:scale-110 transition-transform duration-500 shadow-2xl\">\n                      <svg className=\"w-12 h-12 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4\" />\n                      </svg>\n                    </div>\n                    <h3 className={`text-4xl font-bold text-white mb-4 ${language === 'ar' ? 'font-arabic' : ''}`}>\n                      {language === 'en' ? 'Enterprise Architecture' : 'هندسة المؤسسة'}\n                    </h3>\n                    <p className={`text-white/80 text-lg ${language === 'ar' ? 'font-arabic' : ''}`}>\n                      {language === 'en' ? 'Frameworks & Models' : 'الأطر والنماذج'}\n                    </p>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        )}\n\n        {/* Data Management Frameworks */}\n        {selectedCategory === 'data-management' && (\n          <div className=\"px-12 py-16\">\n            <div className={`flex items-center mb-8 ${language === 'ar' ? 'flex-row-reverse' : 'flex-row'}`}>\n              <button\n                onClick={() => setSelectedCategory('main')}\n                className=\"p-2 rounded-lg hover:bg-gray-100 transition-colors\"\n                style={{ color: 'var(--emerald-green)' }}\n              >\n                <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d={language === 'ar' ? 'M9 5l7 7-7 7' : 'M15 19l-7-7 7-7'} />\n                </svg>\n              </button>\n              <h2 className={`text-3xl font-bold ${language === 'ar' ? 'mr-4 font-arabic' : 'ml-4'}`} style={{ color: 'var(--charcoal-grey)' }}>\n                {language === 'en' ? 'Data Management Frameworks' : 'أطر إدارة البيانات'}\n              </h2>\n            </div>\n\n            <div className={`flex gap-12 ${language === 'ar' ? 'flex-row-reverse' : 'flex-row'}`}>\n              {/* NDMO Card */}\n              <div className=\"flex-1 group cursor-pointer\" onClick={() => router.push('/frameworks/ndmo')}>\n                <div className=\"relative overflow-hidden rounded-3xl h-80 transition-all duration-500 hover:scale-105 hover:shadow-2xl\">\n                  <div\n                    className=\"absolute inset-0\"\n                    style={{ background: 'linear-gradient(135deg, var(--emerald-green) 0%, var(--deep-emerald) 100%)' }}\n                  ></div>\n\n                  {/* Saudi Flag */}\n                  <div className=\"absolute top-4 right-4 px-3 py-1 bg-green-600 text-white text-xs font-bold rounded-full flex items-center gap-2\">\n                    <span>🇸🇦</span>\n                    <span>{language === 'en' ? 'Saudi Arabia' : 'السعودية'}</span>\n                  </div>\n\n                  <div className=\"relative h-full flex flex-col justify-center items-center text-center p-8\">\n                    <h3 className={`text-5xl font-bold text-white mb-4 ${language === 'ar' ? 'font-arabic' : ''}`}>\n                      NDMO\n                    </h3>\n                    <p className={`text-white/80 text-lg ${language === 'ar' ? 'font-arabic' : ''}`}>\n                      {language === 'en' ? 'National Data Management Office' : 'مكتب إدارة البيانات الوطني'}\n                    </p>\n                  </div>\n                </div>\n              </div>\n\n              {/* NPC Card */}\n              <div className=\"flex-1 group cursor-pointer\" onClick={() => router.push('/frameworks/npc')}>\n                <div className=\"relative overflow-hidden rounded-3xl h-80 transition-all duration-500 hover:scale-105 hover:shadow-2xl\">\n                  <div\n                    className=\"absolute inset-0\"\n                    style={{ background: 'linear-gradient(135deg, var(--deep-emerald) 0%, var(--charcoal-grey) 100%)' }}\n                  ></div>\n\n                  {/* Qatar Flag */}\n                  <div className=\"absolute top-4 right-4 px-3 py-1 bg-red-600 text-white text-xs font-bold rounded-full flex items-center gap-2\">\n                    <span>🇶🇦</span>\n                    <span>{language === 'en' ? 'Qatar' : 'قطر'}</span>\n                  </div>\n\n                  <div className=\"relative h-full flex flex-col justify-center items-center text-center p-8\">\n                    <h3 className={`text-5xl font-bold text-white mb-4 ${language === 'ar' ? 'font-arabic' : ''}`}>\n                      NPC\n                    </h3>\n                    <p className={`text-white/80 text-lg ${language === 'ar' ? 'font-arabic' : ''}`}>\n                      {language === 'en' ? 'National Planning Council' : 'مجلس التخطيط الوطني'}\n                    </p>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        )}\n\n        {/* Enterprise Architecture Frameworks */}\n        {selectedCategory === 'enterprise-architecture' && (\n          <div className=\"px-12 py-16\">\n            <div className={`flex items-center mb-8 ${language === 'ar' ? 'flex-row-reverse' : 'flex-row'}`}>\n              <button\n                onClick={() => setSelectedCategory('main')}\n                className=\"p-2 rounded-lg hover:bg-gray-100 transition-colors\"\n                style={{ color: 'var(--emerald-green)' }}\n              >\n                <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d={language === 'ar' ? 'M9 5l7 7-7 7' : 'M15 19l-7-7 7-7'} />\n                </svg>\n              </button>\n              <h2 className={`text-3xl font-bold ${language === 'ar' ? 'mr-4 font-arabic' : 'ml-4'}`} style={{ color: 'var(--charcoal-grey)' }}>\n                {language === 'en' ? 'Enterprise Architecture Frameworks' : 'أطر هندسة المؤسسة'}\n              </h2>\n            </div>\n\n            <div className={`flex gap-12 ${language === 'ar' ? 'flex-row-reverse' : 'flex-row'}`}>\n              {/* NOURA Card */}\n              <div className=\"flex-1 group cursor-pointer\" onClick={() => router.push('/frameworks/noura')}>\n                <div className=\"relative overflow-hidden rounded-3xl h-80 transition-all duration-500 hover:scale-105 hover:shadow-2xl\">\n                  <div\n                    className=\"absolute inset-0\"\n                    style={{ background: 'linear-gradient(135deg, var(--emerald-green) 0%, var(--deep-emerald) 100%)' }}\n                  ></div>\n\n                  {/* Saudi Flag */}\n                  <div className=\"absolute top-4 right-4 px-3 py-1 bg-green-600 text-white text-xs font-bold rounded-full flex items-center gap-2\">\n                    <span>🇸🇦</span>\n                    <span>{language === 'en' ? 'Saudi Arabia' : 'السعودية'}</span>\n                  </div>\n\n                  <div className=\"relative h-full flex flex-col justify-center items-center text-center p-8\">\n                    <h3 className={`text-5xl font-bold text-white mb-4 ${language === 'ar' ? 'font-arabic' : ''}`}>\n                      NOURA\n                    </h3>\n                    <p className={`text-white/80 text-lg ${language === 'ar' ? 'font-arabic' : ''}`}>\n                      {language === 'en' ? 'National Enterprise Architecture' : 'هندسة المؤسسة الوطنية'}\n                    </p>\n                  </div>\n                </div>\n              </div>\n\n              {/* GEA Card */}\n              <div className=\"flex-1 group cursor-pointer\" onClick={() => router.push('/frameworks/gea')}>\n                <div className=\"relative overflow-hidden rounded-3xl h-80 transition-all duration-500 hover:scale-105 hover:shadow-2xl\">\n                  <div\n                    className=\"absolute inset-0\"\n                    style={{ background: 'linear-gradient(135deg, var(--deep-emerald) 0%, var(--charcoal-grey) 100%)' }}\n                  ></div>\n\n                  {/* Qatar Flag */}\n                  <div className=\"absolute top-4 right-4 px-3 py-1 bg-red-600 text-white text-xs font-bold rounded-full flex items-center gap-2\">\n                    <span>🇶🇦</span>\n                    <span>{language === 'en' ? 'Qatar' : 'قطر'}</span>\n                  </div>\n\n                  <div className=\"relative h-full flex flex-col justify-center items-center text-center p-8\">\n                    <h3 className={`text-5xl font-bold text-white mb-4 ${language === 'ar' ? 'font-arabic' : ''}`}>\n                      GEA\n                    </h3>\n                    <p className={`text-white/80 text-lg ${language === 'ar' ? 'font-arabic' : ''}`}>\n                      {language === 'en' ? 'Government Enterprise Architecture' : 'هندسة المؤسسة الحكومية'}\n                    </p>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        )}\n\n\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAaA,MAAM,aAA0B;IAC9B;QAAE,IAAI;QAAQ,MAAM;QAAQ,SAAS;QAAS,MAAM;IAAkB;IACtE;QAAE,IAAI;QAAO,MAAM;QAAO,SAAS;QAAS,MAAM;IAAkB;IACpE;QAAE,IAAI;QAAS,MAAM;QAAS,SAAS;QAAS,MAAM;IAA0B;IAChF;QAAE,IAAI;QAAO,MAAM;QAAO,SAAS;QAAS,MAAM;IAA0B;CAC7E;AAEc,SAAS;IACtB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IACtD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA0D;IACjH,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,eAAe,aAAa,OAAO,CAAC,eAA8B;QACxE,YAAY;IACd,GAAG,EAAE;IAEL,MAAM,UAAU;QACd,IAAI;YACF,OAAO;YACP,UAAU;YACV,aAAa;YACb,aAAa;QACf;QACA,IAAI;YACF,OAAO;YACP,UAAU;YACV,aAAa;YACb,aAAa;QACf;IACF;IAEA,MAAM,8BACJ,8OAAC;QAAI,WAAU;QAAU,MAAK;QAAO,QAAO;QAAe,SAAQ;kBACjE,cAAA,8OAAC;YAAK,eAAc;YAAQ,gBAAe;YAAQ,aAAa;YAAG,GAAE;;;;;;;;;;;IAIzE,qBACE,8OAAC;QAAI,WAAW,GAAG,aAAa,OAAO,eAAe,aAAa;;0BACjE,8OAAC,0HAAA,CAAA,UAAI;gBACH,OAAO,OAAO,CAAC,SAAS,CAAC,KAAK;gBAC9B,UAAU,OAAO,CAAC,SAAS,CAAC,QAAQ;gBACpC,aAAa,OAAO,CAAC,SAAS,CAAC,WAAW;gBAC1C,MAAM;gBACN,aAAa;oBACX;wBAAE,OAAO,aAAa,OAAO,cAAc;wBAAe,MAAM;oBAAa;oBAC7E;wBAAE,OAAO,OAAO,CAAC,SAAS,CAAC,KAAK;oBAAC;iBAClC;;;;;;0BAGH,8OAAC;gBAAI,WAAU;;oBACZ,qBAAqB,wBACpB,8OAAC;wBAAI,WAAU;kCAEb,cAAA,8OAAC;4BAAI,WAAW,CAAC,YAAY,EAAE,aAAa,OAAO,qBAAqB,YAAY;;8CAGlF,8OAAC;oCAAI,WAAU;oCAA8B,SAAS,IAAM,oBAAoB;8CAC9E,cAAA,8OAAC;wCAAI,WAAU;;0DAEb,8OAAC;gDACC,WAAU;gDACV,OAAO;oDACL,YAAY;gDACd;;;;;;0DAIF,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;;;;;kEACf,8OAAC;wDAAI,WAAU;wDAAwF,OAAO;4DAAE,gBAAgB;wDAAK;;;;;;kEACrI,8OAAC;wDAAI,WAAU;wDAAiF,OAAO;4DAAE,gBAAgB;wDAAK;;;;;;;;;;;;0DAIhI,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDACC,WAAU;oDACV,OAAO;wDAAE,OAAO;oDAAO;;;;;;;;;;;0DAK3B,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAI,WAAU;4DAAuB,MAAK;4DAAO,QAAO;4DAAe,SAAQ;sEAC9E,cAAA,8OAAC;gEAAK,eAAc;gEAAQ,gBAAe;gEAAQ,aAAa;gEAAG,GAAE;;;;;;;;;;;;;;;;kEAGzE,8OAAC;wDAAG,WAAW,CAAC,mCAAmC,EAAE,aAAa,OAAO,gBAAgB,IAAI;kEAC1F,aAAa,OAAO,oBAAoB;;;;;;kEAE3C,8OAAC;wDAAE,WAAW,CAAC,sBAAsB,EAAE,aAAa,OAAO,gBAAgB,IAAI;kEAC5E,aAAa,OAAO,2BAA2B;;;;;;;;;;;;;;;;;;;;;;;8CAOxD,8OAAC;oCAAI,WAAU;oCAA8B,SAAS,IAAM,oBAAoB;8CAC9E,cAAA,8OAAC;wCAAI,WAAU;;0DAEb,8OAAC;gDACC,WAAU;gDACV,OAAO;oDACL,YAAY;gDACd;;;;;;0DAIF,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;;;;;kEACf,8OAAC;wDAAI,WAAU;wDAAyF,OAAO;4DAAE,gBAAgB;wDAAK;;;;;;kEACtI,8OAAC;wDAAI,WAAU;wDAAkF,OAAO;4DAAE,gBAAgB;wDAAK;;;;;;;;;;;;0DAIjI,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDACC,WAAU;oDACV,OAAO;wDAAE,OAAO;oDAAO;;;;;;;;;;;0DAK3B,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAI,WAAU;4DAAuB,MAAK;4DAAO,QAAO;4DAAe,SAAQ;sEAC9E,cAAA,8OAAC;gEAAK,eAAc;gEAAQ,gBAAe;gEAAQ,aAAa;gEAAG,GAAE;;;;;;;;;;;;;;;;kEAGzE,8OAAC;wDAAG,WAAW,CAAC,mCAAmC,EAAE,aAAa,OAAO,gBAAgB,IAAI;kEAC1F,aAAa,OAAO,4BAA4B;;;;;;kEAEnD,8OAAC;wDAAE,WAAW,CAAC,sBAAsB,EAAE,aAAa,OAAO,gBAAgB,IAAI;kEAC5E,aAAa,OAAO,wBAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oBAU1D,qBAAqB,mCACpB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAW,CAAC,uBAAuB,EAAE,aAAa,OAAO,qBAAqB,YAAY;;kDAC7F,8OAAC;wCACC,SAAS,IAAM,oBAAoB;wCACnC,WAAU;wCACV,OAAO;4CAAE,OAAO;wCAAuB;kDAEvC,cAAA,8OAAC;4CAAI,WAAU;4CAAU,MAAK;4CAAO,QAAO;4CAAe,SAAQ;sDACjE,cAAA,8OAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAa;gDAAG,GAAG,aAAa,OAAO,iBAAiB;;;;;;;;;;;;;;;;kDAG/G,8OAAC;wCAAG,WAAW,CAAC,mBAAmB,EAAE,aAAa,OAAO,qBAAqB,QAAQ;wCAAE,OAAO;4CAAE,OAAO;wCAAuB;kDAC5H,aAAa,OAAO,+BAA+B;;;;;;;;;;;;0CAIxD,8OAAC;gCAAI,WAAW,CAAC,YAAY,EAAE,aAAa,OAAO,qBAAqB,YAAY;;kDAElF,8OAAC;wCAAI,WAAU;wCAA8B,SAAS,IAAM,OAAO,IAAI,CAAC;kDACtE,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDACC,WAAU;oDACV,OAAO;wDAAE,YAAY;oDAA6E;;;;;;8DAIpG,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;sEAAK;;;;;;sEACN,8OAAC;sEAAM,aAAa,OAAO,iBAAiB;;;;;;;;;;;;8DAG9C,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAG,WAAW,CAAC,mCAAmC,EAAE,aAAa,OAAO,gBAAgB,IAAI;sEAAE;;;;;;sEAG/F,8OAAC;4DAAE,WAAW,CAAC,sBAAsB,EAAE,aAAa,OAAO,gBAAgB,IAAI;sEAC5E,aAAa,OAAO,oCAAoC;;;;;;;;;;;;;;;;;;;;;;;kDAOjE,8OAAC;wCAAI,WAAU;wCAA8B,SAAS,IAAM,OAAO,IAAI,CAAC;kDACtE,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDACC,WAAU;oDACV,OAAO;wDAAE,YAAY;oDAA6E;;;;;;8DAIpG,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;sEAAK;;;;;;sEACN,8OAAC;sEAAM,aAAa,OAAO,UAAU;;;;;;;;;;;;8DAGvC,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAG,WAAW,CAAC,mCAAmC,EAAE,aAAa,OAAO,gBAAgB,IAAI;sEAAE;;;;;;sEAG/F,8OAAC;4DAAE,WAAW,CAAC,sBAAsB,EAAE,aAAa,OAAO,gBAAgB,IAAI;sEAC5E,aAAa,OAAO,8BAA8B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oBAUhE,qBAAqB,2CACpB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAW,CAAC,uBAAuB,EAAE,aAAa,OAAO,qBAAqB,YAAY;;kDAC7F,8OAAC;wCACC,SAAS,IAAM,oBAAoB;wCACnC,WAAU;wCACV,OAAO;4CAAE,OAAO;wCAAuB;kDAEvC,cAAA,8OAAC;4CAAI,WAAU;4CAAU,MAAK;4CAAO,QAAO;4CAAe,SAAQ;sDACjE,cAAA,8OAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAa;gDAAG,GAAG,aAAa,OAAO,iBAAiB;;;;;;;;;;;;;;;;kDAG/G,8OAAC;wCAAG,WAAW,CAAC,mBAAmB,EAAE,aAAa,OAAO,qBAAqB,QAAQ;wCAAE,OAAO;4CAAE,OAAO;wCAAuB;kDAC5H,aAAa,OAAO,uCAAuC;;;;;;;;;;;;0CAIhE,8OAAC;gCAAI,WAAW,CAAC,YAAY,EAAE,aAAa,OAAO,qBAAqB,YAAY;;kDAElF,8OAAC;wCAAI,WAAU;wCAA8B,SAAS,IAAM,OAAO,IAAI,CAAC;kDACtE,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDACC,WAAU;oDACV,OAAO;wDAAE,YAAY;oDAA6E;;;;;;8DAIpG,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;sEAAK;;;;;;sEACN,8OAAC;sEAAM,aAAa,OAAO,iBAAiB;;;;;;;;;;;;8DAG9C,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAG,WAAW,CAAC,mCAAmC,EAAE,aAAa,OAAO,gBAAgB,IAAI;sEAAE;;;;;;sEAG/F,8OAAC;4DAAE,WAAW,CAAC,sBAAsB,EAAE,aAAa,OAAO,gBAAgB,IAAI;sEAC5E,aAAa,OAAO,qCAAqC;;;;;;;;;;;;;;;;;;;;;;;kDAOlE,8OAAC;wCAAI,WAAU;wCAA8B,SAAS,IAAM,OAAO,IAAI,CAAC;kDACtE,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDACC,WAAU;oDACV,OAAO;wDAAE,YAAY;oDAA6E;;;;;;8DAIpG,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;sEAAK;;;;;;sEACN,8OAAC;sEAAM,aAAa,OAAO,UAAU;;;;;;;;;;;;8DAGvC,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAG,WAAW,CAAC,mCAAmC,EAAE,aAAa,OAAO,gBAAgB,IAAI;sEAAE;;;;;;sEAG/F,8OAAC;4DAAE,WAAW,CAAC,sBAAsB,EAAE,aAAa,OAAO,gBAAgB,IAAI;sEAC5E,aAAa,OAAO,uCAAuC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAalF", "debugId": null}}]}